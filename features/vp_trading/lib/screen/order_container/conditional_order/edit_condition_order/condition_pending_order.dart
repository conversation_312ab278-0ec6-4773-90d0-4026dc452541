import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/cubit/order_edit/edit_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/screen/order_container/conditional_order/edit_condition_order/edit_awaiting_order_widget.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';

void opendEditConditionPendingOrderBottomSheet({
  required BuildContext context,
  required ConditionOrderBookModel item,
  required VoidCallback onEditSuccess,
}) {
  VPPopup.bottomSheet(
    MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => EditConditionOrderCubit()),
        BlocProvider(create: (context) => ValidateConditionOrderCubit()),
        BlocProvider(create: (context) => ValidateOrderCubit()),
        BlocProvider(
          create:
              (context) => PlaceOrderCubit(
                symbol: item.symbol ?? '',
                action:
                    item.orderTypeEnum == OrderTypeEnum.buy
                        ? OrderAction.buy
                        : OrderAction.sell,
              ),
        ),
        BlocProvider(create: (context) => StockInfoCubit()),
        BlocProvider(create: (context) => AvailableTradeCubit()),
      ],
      child: _EditConditionPendingOrder(
        item: item,
        onEditSuccess: onEditSuccess,
      ),
    ),
  ).showSheet(context);
}

class _EditConditionPendingOrder extends StatefulWidget {
  const _EditConditionPendingOrder({
    required this.item,
    required this.onEditSuccess,
  });

  final ConditionOrderBookModel item;
  final VoidCallback onEditSuccess;

  @override
  State<_EditConditionPendingOrder> createState() =>
      _EditConditionPendingOrderState();
}

class _EditConditionPendingOrderState
    extends State<_EditConditionPendingOrder> {
  @override
  void initState() {
    super.initState();

    // Initialize stock info for the symbol
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.item.symbol != null) {
        context.read<StockInfoCubit>().loadData(widget.item.symbol!);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.item.orderTypeEnum == OrderTypeEnum.buy
              ? "Lệnh chờ mua"
              : "Lệnh chờ bán",
          style: vpTextStyle.body14.copyColor(vpColor.textPrimary),
        ),

        VPTextField(
          hintText: widget.item.symbol ?? '',
          textAlign: TextAlign.start,
          inputType: InputType.disabled,
        ),
        const SizedBox(height: 16),

        // Edit Awaiting Order Widget
        EditAwaitingOrderWidget(
          item: widget.item,
          onEditSuccess: widget.onEditSuccess,
        ),
      ],
    );
  }
}
