import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';

class EditEffectiveTimeWidget extends StatefulWidget {
  const EditEffectiveTimeWidget({
    super.key,
    this.initialFromDate,
    this.initialToDate,
  });

  final DateTime? initialFromDate;
  final DateTime? initialToDate;

  @override
  State<EditEffectiveTimeWidget> createState() =>
      _EditEffectiveTimeWidgetState();
}

class _EditEffectiveTimeWidgetState extends State<EditEffectiveTimeWidget> {
  late DateTime _startDate;
  late DateTime _endDate;

  @override
  void initState() {
    super.initState();

    // Initialize with provided dates or default to current date
    _startDate = widget.initialFromDate ?? DateTime.now();
    _endDate = widget.initialToDate ?? DateTime.now();

    // Set initial values to validation cubit
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ValidateConditionOrderCubit>().setEffectiveTime(
        _startDate,
        _endDate,
      );
    });
  }

  void _onDateTimeChanged(({DateTime endDate, DateTime startDate}) data) {
    setState(() {
      _startDate = data.startDate;
      _endDate = data.endDate;
    });

    // Update validation cubit
    context.read<ValidateConditionOrderCubit>().setEffectiveTime(
      data.startDate,
      data.endDate,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            VPTradingLocalize.current.trading_effective_time,
            style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
          ),
          const SizedBox(height: 4),
          VPDateTimeHolderCommon(
            startDate: _startDate,
            endDate: _endDate,
            minDate: DateTime.now(),
            onDateTimeChanged: _onDateTimeChanged,
          ),
        ],
      ),
    );
  }
}
