import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_trading/cubit/order_edit/edit_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_condition_order/validate_condition_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/screen/order_container/conditional_order/edit_condition_order/edit_effective_time_widget.dart';
import 'package:vp_trading/screen/place_order/awaiting/activation_conditions_widget.dart';
import 'package:vp_trading/screen/place_order/awaiting/awaiting_price_volume_widget.dart';
import 'package:vp_trading/screen/place_order/widgets/choice_dilution_action_widget.dart';

class EditAwaitingOrderWidget extends StatefulWidget {
  const EditAwaitingOrderWidget({
    super.key,
    required this.item,
    required this.onEditSuccess,
  });

  final ConditionOrderBookModel item;
  final VoidCallback onEditSuccess;

  @override
  State<EditAwaitingOrderWidget> createState() =>
      _EditAwaitingOrderWidgetState();
}

class _EditAwaitingOrderWidgetState extends State<EditAwaitingOrderWidget> {
  late final TextEditingController _activationPriceController;

  final _activationPriceKey = GlobalKey<ActivationConditionsWidgetState>();

  @override
  void initState() {
    super.initState();

    // Initialize controllers with existing data
    // Note: Server returns price in thousands, need to divide by 1000 for display
    _activationPriceController = TextEditingController(
      text:
          widget.item.activePrice != null
              ? (widget.item.activePrice! / 1000).toString()
              : '',
    );

    // Initialize validation and state after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeState();
    });
  }

  void _initializeState() {
    // Set activation conditions type
    final activationType = _parseActivationType(widget.item.activeType);
    context.read<ValidateConditionOrderCubit>().setActivationConditions(
      activationType,
    );

    // Note: Effective time will be set by EditEffectiveTimeWidget

    // Initialize validation with current values
    if (_activationPriceController.text.isNotEmpty) {
      context.read<ValidateOrderCubit>().onChangeActivationPrice(
        _activationPriceController.text,
      );
    }

    // Pre-fill order price and volume if available
    // Note: Server returns price in thousands, need to divide by 1000 for display
    if (widget.item.price != null && widget.item.price!.isNotEmpty) {
      final priceValue = double.tryParse(widget.item.price!) ?? 0;
      final displayPrice = (priceValue / 1000).toString();
      context.read<ValidateOrderCubit>().onChangePrice(displayPrice);
    }
    if (widget.item.qty != null) {
      context.read<ValidateOrderCubit>().onChangeVolumne(
        widget.item.qty.toString(),
      );
    }
  }

  ActivationConditionsType _parseActivationType(String? activeType) {
    switch (activeType?.toUpperCase()) {
      case 'GE':
      case 'GREATER_THAN':
        return ActivationConditionsType.greaterThan;
      case 'LE':
      case 'LESS_THAN':
      default:
        return ActivationConditionsType.lessThan;
    }
  }

  DateTime? _parseDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;

    try {
      // Assuming date format is dd/mm/yyyy or similar
      final parts = dateString.split('/');
      if (parts.length == 3) {
        final day = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        return DateTime(year, month, day);
      }
    } catch (e) {
      // Handle parsing error
    }
    return null;
  }

  String _formatActivationType(ActivationConditionsType type) {
    switch (type) {
      case ActivationConditionsType.greaterThan:
        return 'GE';
      case ActivationConditionsType.lessThan:
        return 'LE';
    }
  }

  void _handleSave() {
    final validateOrderState = context.read<ValidateOrderCubit>().state;
    final validateConditionState =
        context.read<ValidateConditionOrderCubit>().state;

    // Check if all validations pass
    if (validateOrderState.errorActivationPrice.isError ||
        validateOrderState.errorPrice.isError ||
        validateOrderState.errorVolume.isError) {
      // Show validation errors
      return;
    }

    // Get current values
    // Note: Convert display prices (divided by 1000) back to server format (multiply by 1000)
    final activePrice =
        (double.tryParse(_activationPriceController.text) ?? 0.0) * 1000;
    final price =
        (double.tryParse(validateOrderState.currentPrice ?? '0') ?? 0.0) * 1000;
    final qty = int.tryParse(validateOrderState.currentVolume ?? '0') ?? 0;
    final activeType = _formatActivationType(
      validateConditionState.activationType,
    );

    // Call update API
    context.read<EditConditionOrderCubit>().updateConditionOrder(
      orderId: widget.item.orderId ?? '',
      accountId: widget.item.accountId ?? '',
      symbol: widget.item.symbol ?? '',
      market: 'EQUITY', // Default market
      orderType: widget.item.orderType ?? 'BB',
      side: widget.item.side ?? 'B',
      activePrice: activePrice,
      activeType: activeType,
      price: price,
      qty: qty,
      fromDate: validateConditionState.fromDate ?? '',
      toDate: validateConditionState.toDate ?? '',
      diluationAction: widget.item.diluationAction,
    );
  }

  @override
  void dispose() {
    _activationPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<EditConditionOrderCubit, EditConditionOrderState>(
      listener: (context, state) {
        if (state.status == EditConditionOrderStatus.success) {
          widget.onEditSuccess();
          Navigator.of(context).pop();
        } else if (state.status == EditConditionOrderStatus.failure) {
          // Show error message
          context.showSnackBar(
            content: state.errorMessage ?? "-",
            snackBarType: VPSnackBarType.error,
          );
        }
      },
      child: Column(
        children: [
          // Activation Conditions
          ActivationConditionsWidget(
            key: _activationPriceKey,
            priceController: _activationPriceController,
          ),

          // Price and Volume
          const AwaitingPriceVolumeWidget(),

          const SizedBox(height: 8),

          // Effective Time
          EditEffectiveTimeWidget(
            initialFromDate: _parseDate(widget.item.fromDate),
            initialToDate: _parseDate(widget.item.toDate),
          ),

          const SizedBox(height: 8),

          // Dilution Action
          const ChoiceDilutionActionWidget(),

          const SizedBox(height: 24),

          // Action Buttons
          BlocBuilder<EditConditionOrderCubit, EditConditionOrderState>(
            builder: (context, editState) {
              return Row(
                children: [
                  Expanded(
                    child: VpsButton.secondarySmall(
                      title: 'Hủy',
                      onPressed: () => Navigator.of(context).pop(),
                      alignment: Alignment.center,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: VpsButton.primarySmall(
                      title:
                          editState.status == EditConditionOrderStatus.loading
                              ? 'Đang lưu...'
                              : 'Lưu',
                      onPressed:
                          editState.status == EditConditionOrderStatus.loading
                              ? null
                              : _handleSave,
                      alignment: Alignment.center,
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
