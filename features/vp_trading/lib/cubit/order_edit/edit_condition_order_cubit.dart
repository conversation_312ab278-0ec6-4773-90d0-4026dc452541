import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:vp_common/error/show_error.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/repository/command_history_repository.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';

part 'edit_condition_order_state.dart';

class EditConditionOrderCubit extends Cubit<EditConditionOrderState> {
  EditConditionOrderCubit() : super(const EditConditionOrderState());

  final CommandHistoryRepository _commandHistoryRepository =
      GetIt.instance<CommandHistoryRepository>();

  /// Reset error message
  void resetErrorMessage() {
    emit(state.copyWith(errorMessage: null));
  }

  /// Reset success message
  void resetSuccessMessage() {
    emit(state.copyWith(successMessage: null));
  }

  /// Update condition order
  Future<void> updateConditionOrder({
    required String orderId,
    required String accountId,
    required String symbol,
    required String market,
    required String orderType,
    required String side,
    required double activePrice,
    required String activeType,
    required double price,
    required int qty,
    required String fromDate,
    required String toDate,
    String? diluationAction,
  }) async {
    try {
      if (isClosed) return;

      emit(
        state.copyWith(
          status: EditConditionOrderStatus.loading,
          errorMessage: null,
          successMessage: null,
        ),
      );

      // Create condition order request
      final conditionInfo = ConditionInfo(
        symbol: symbol,
        qty: qty,
        side: side,
        type: 'LO', // Default to LO for condition orders
        price: price,
        fromDate: fromDate,
        toDate: toDate,
        activePrice: activePrice,
        activeType: activeType,
      );

      final updateRequest = ConditionOrderRequestModel(
        requestId: AppHelper().genXRequestID(),
        market: market,
        via: 'V',
        orderType: orderType,
        accountId: accountId,
        conditionInfo: conditionInfo,
      );

      // For now, we'll use the create API since there's no specific update API
      // In a real implementation, you might need to delete the old order first
      // and then create a new one, or use a specific update endpoint if available
      
      // TODO: Implement actual update API call
      // This is a placeholder - you'll need to implement the actual API call
      // based on your backend's condition order update endpoint
      
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate API call
      
      if (isClosed) return;

      emit(
        state.copyWith(
          status: EditConditionOrderStatus.success,
          successMessage: 'Condition order updated successfully',
        ),
      );
    } catch (error) {
      if (isClosed) return;

      var message = (await getErrorMessage(error));
      emit(
        state.copyWith(
          status: EditConditionOrderStatus.failure,
          errorMessage: message,
        ),
      );
      debugPrintStack(stackTrace: StackTrace.current);
    } finally {
      if (!isClosed) {
        emit(state.copyWith(status: EditConditionOrderStatus.initial));
      }
    }
  }
}
